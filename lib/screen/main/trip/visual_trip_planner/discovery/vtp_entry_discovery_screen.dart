import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:flutter/material.dart';
import 'package:family_app/utils/log/app_logger.dart';

class VTPEntryDiscoveryScreen extends StatefulWidget {
  const VTPEntryDiscoveryScreen({
    required this.initialData,
    required this.onComplete,
    super.key,
  });

  final Map<String, dynamic> initialData;
  final Function(Map<String, dynamic>) onComplete;
  @override
  State<VTPEntryDiscoveryScreen> createState() =>
      _VTPEntryDiscoveryScreenState();
}

class _VTPEntryDiscoveryScreenState extends State<VTPEntryDiscoveryScreen> {
  final List<String> _selectedVibes = [];
  final TextEditingController _searchController = TextEditingController();
  bool _hasSelection = false;

  final List<Map<String, dynamic>> _vibeOptions = [
    {
      'id': 'adventure',
      'title': 'Adventure & Outdoors',
      'icon': Icons.hiking,
      'color': Colors.green,
    },
    {
      'id': 'city',
      'title': 'City Exploration & Culture',
      'icon': Icons.location_city,
      'color': Colors.blue,
    },
    {
      'id': 'relaxation',
      'title': 'Relaxation & Wellness',
      'icon': Icons.spa,
      'color': Colors.purple,
    },
    {
      'id': 'foodie',
      'title': 'Foodie Journey',
      'icon': Icons.restaurant,
      'color': Colors.orange,
    },
    {
      'id': 'historical',
      'title': 'Historical Deep Dive',
      'icon': Icons.history,
      'color': Colors.brown,
    },
  ];

  @override
  void initState() {
    super.initState();
    _initializeFromAiTripIntent();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  /// Initialize the form with data from aiTripIntent
  void _initializeFromAiTripIntent() {
    // Pre-fill search term with destination from aiTripIntent
    final extractedDestination =
        widget.initialData['extracted_destination'] as String?;
    if (extractedDestination != null && extractedDestination.isNotEmpty) {
      _searchController.text = extractedDestination;
      setState(() {
        _hasSelection = true;
      });
      AppLogger.d(
          'Pre-filled search term with destination: $extractedDestination');
    }

    // Log other available data for debugging
    final extractedDuration =
        widget.initialData['extracted_duration'] as String?;
    final extractedTime = widget.initialData['extracted_time'] as String?;
    AppLogger.d('Available aiTripIntent data:');
    AppLogger.d('  - destination: $extractedDestination');
    AppLogger.d('  - duration: $extractedDuration');
    AppLogger.d('  - time: $extractedTime');
  }

  void _handleVibeSelection(String vibeId) {
    setState(() {
      if (_selectedVibes.contains(vibeId)) {
        _selectedVibes.remove(vibeId);
      } else {
        _selectedVibes.add(vibeId);
      }
      _hasSelection =
          _selectedVibes.isNotEmpty || _searchController.text.isNotEmpty;
    });
  }

  void _handleSearchChanged(String value) {
    setState(() {
      _hasSelection = _selectedVibes.isNotEmpty || value.isNotEmpty;
    });
  }

  void _handleNext() {
    final data = {
      'selectedVibes': _selectedVibes,
      'searchTerm': _searchController.text,
      'trip_intent': 'Discovery',
    };

    AppLogger.d("data: $data");

    if (_searchController.text.isNotEmpty) {
      // If there's a search term, go to destination focus
      widget.onComplete({
        ...data,
        'nextScreen': 'destination_focus',
      });
    } else {
      // If only vibes are selected,
      // remind user to add a search term
      showDialog(
        context: context,
        builder: (context) => AlertDialog(
          title: Text(LocaleKeys.trip_planner_discovery_no_search_term.tr()),
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            LocaleKeys.trip_planner_discovery_subtitle.tr(),
            style: AppStyle.bold18V2(),
          ),
          const SizedBox(height: 24),

          // Search Bar
          TextField(
            controller: _searchController,
            onChanged: _handleSearchChanged,
            decoration: InputDecoration(
              hintText: LocaleKeys.trip_planner_discovery_search_hint.tr(),
              prefixIcon: const Icon(Icons.search),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          ),
          const SizedBox(height: 24),

          // Vibe Selection Grid
          Text(
            LocaleKeys.trip_planner_discovery_vibes_title.tr(),
            style: AppStyle.bold18V2(),
          ),
          const SizedBox(height: 16),
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              childAspectRatio: 1.5,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
            ),
            itemCount: _vibeOptions.length,
            itemBuilder: (context, index) {
              final vibe = _vibeOptions[index];
              final isSelected = _selectedVibes.contains(vibe['id']);

              return InkWell(
                onTap: () => _handleVibeSelection(vibe['id']),
                child: Container(
                  decoration: BoxDecoration(
                    color: isSelected
                        ? vibe['color'].withAlpha(51) // Alpha 51 = 20% opacity
                        : Colors.grey[100],
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected ? vibe['color'] : Colors.grey[300]!,
                      width: 2,
                    ),
                  ),
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        vibe['icon'],
                        color: vibe['color'],
                        size: 32,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        vibe['title'],
                        style: AppStyle.regular14V2(
                          color: isSelected ? vibe['color'] : Colors.black87,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            },
          ),
          const SizedBox(height: 32),

          // Next Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _hasSelection ? _handleNext : null,
              style: ElevatedButton.styleFrom(
                padding: const EdgeInsets.symmetric(vertical: 16),
                backgroundColor:
                    const Color(0xFF4E46B4), // Purple color for consistency
                foregroundColor: Colors.white,
                elevation: 4, // Increased elevation for better visibility
                disabledBackgroundColor: const Color(0xFF4E46B4).withAlpha(
                    128), // Semi-transparent purple when disabled (alpha 128 = 50%)
                disabledForegroundColor: Colors.white.withAlpha(
                    178), // Semi-transparent white text when disabled (alpha 178 = 70%)
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Text(
                    LocaleKeys.next.tr(),
                    style: AppStyle.bold18V2(
                        color: Colors.white), // Increased font size
                  ),
                  const SizedBox(width: 8),
                  const Icon(Icons.arrow_forward,
                      color: Colors.white), // Added icon
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

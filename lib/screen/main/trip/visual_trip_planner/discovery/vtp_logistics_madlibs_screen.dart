import 'dart:convert';

import 'package:easy_localization/easy_localization.dart';
import 'package:family_app/config/lang/locale_keys.g.dart';
import 'package:family_app/config/theme/style/style_theme.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/constants/vtp_constants.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/discovery/vtp_discovery_cubit.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/model/trip_plan_data.dart';
import 'package:family_app/utils/log/app_logger.dart';
import 'package:flutter/material.dart';

class VTPLogisticsMadLibsSheet extends StatefulWidget {
  const VTPLogisticsMadLibsSheet({
    required this.initialData,
    required this.onComplete,
    this.discoveryCubit,
    super.key,
  });

  final Map<String, dynamic> initialData;
  final Function(Map<String, dynamic>) onComplete;
  final VTPDiscoveryCubit? discoveryCubit;

  @override
  State<VTPLogisticsMadLibsSheet> createState() =>
      _VTPLogisticsMadLibsSheetState();
}

class _VTPLogisticsMadLibsSheetState extends State<VTPLogisticsMadLibsSheet> {
  final _formKey = GlobalKey<FormState>();
  late DateTime _startDate;
  late DateTime _endDate;
  late String _travelParty;
  late String _budget;
  late String _accommodationType;
  late String _transportationType;
  late String _activityStyle;

  @override
  void initState() {
    super.initState();
    _initializeDatesFromAiTripIntent();

    // Pre-fill options as requested
    _travelParty = 'Family Fun';
    _budget = 'Budget-Friendly';
    _accommodationType = 'Hotel';
    _transportationType = 'Public Transit';
    _activityStyle = 'Culture Enthusiast';
  }

  /// Initialize dates from aiTripIntent data
  void _initializeDatesFromAiTripIntent() {
    // Default dates
    _startDate = DateTime.now();
    _endDate = DateTime.now().add(const Duration(days: 7));

    // Try to use parsed start date from aiTripIntent
    final parsedStartDate =
        widget.initialData['parsed_start_date'] as DateTime?;
    if (parsedStartDate != null) {
      _startDate = parsedStartDate;
      AppLogger.d('Using start date from aiTripIntent: $_startDate');
    }

    // Try to use parsed duration from aiTripIntent
    final parsedDuration =
        widget.initialData['parsed_duration'] as Map<String, dynamic>?;
    if (parsedDuration != null && parsedDuration['days'] != null) {
      final days = parsedDuration['days'] as int;
      _endDate = _startDate.add(Duration(days: days));
      AppLogger.d(
          'Using duration from aiTripIntent: $days days, end date: $_endDate');
    } else if (parsedStartDate != null) {
      // If we have start date but no duration, use default 7 days
      _endDate = _startDate.add(const Duration(days: 7));
    }

    AppLogger.d('Initialized dates - Start: $_startDate, End: $_endDate');
  }

  Future<void> _selectDate(BuildContext context, bool isStartDate) async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
      initialDateRange: DateTimeRange(start: _startDate, end: _endDate),
    );
    if (picked != null) {
      setState(() {
        _startDate = picked.start;
        _endDate = picked.end;
      });
    }
  }

  // Removed unused _handleSubmit method

  void _handleGetMyPlan() {
    if (_formKey.currentState?.validate() ?? false) {
      _formKey.currentState?.save();

      final discoveryData = DiscoveryTripData(
        searchTerm: widget.initialData['searchTerm'],
        selectedVibes:
            List<String>.from(widget.initialData['selectedVibes'] ?? []),
        destination: widget.initialData['destination'],
        startDate: _startDate,
        endDate: _endDate,
        travelParty: _travelParty,
        budget: _budget,
        accommodationType: _accommodationType,
        transportationType: _transportationType,
        activityStyle: _activityStyle,
      );

      final tripPlanData = TripPlanData(
        tripIntent: 'Discovery',
        data: discoveryData.toJson(),
      );

      // Send to AI chat using the VTP discovery cubit
      if (widget.discoveryCubit != null) {
        _sendTripPlanToAI(tripPlanData);
      } else {
        AppLogger.e("VTP Discovery Cubit is null, cannot send trip plan to AI");
        // Show error message to user
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Error: Unable to connect to AI service'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  /// Send the trip plan data to the AI service using the WebSocket connection
  void _sendTripPlanToAI(TripPlanData tripPlanData) {
    try {
      // Convert the trip plan data to a JSON string
      final tripPlanJson = jsonEncode(tripPlanData.toJson());

      // Create the WebSocket message in the format required by the AI service
      final jsonMessage = jsonEncode({
        "command": "ai_chat",
        "message": tripPlanJson,
        "auto_create": "false",
        "prompt": AIPrompts.vtpPlan // Using a different prompt for planning
      });

      AppLogger.d("Sending trip plan message: $jsonMessage");

      // Send the message through the WebSocket channel
      widget.discoveryCubit!.sendWebSocketMessage(jsonMessage);

      // Close the bottom sheet and return to the previous screen
      Navigator.pop(context);

      //TODO: show a progress bar or indicator saying that the
    } catch (e) {
      AppLogger.e("Error sending trip plan to AI: $e");
      // Show error message to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        bottom: MediaQuery.of(context).viewInsets.bottom,
      ),
      child: SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Drag handle
              Center(
                child: Container(
                  width: 40,
                  height: 4,
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              ),
              Text(
                LocaleKeys.trip_planner_logistics_title.tr(),
                style: AppStyle.bold24V2(),
              ),
              const SizedBox(height: 24),

              // Date Selection
              _buildMadLibsField(
                title: LocaleKeys.trip_planner_logistics_dates.tr(),
                value:
                    '${DateFormat('MMM d').format(_startDate)} - ${DateFormat('MMM d, y').format(_endDate)}',
                onTap: () => _selectDate(context, true),
                icon: Icons.calendar_today,
              ),

              // Travel Party
              _buildMadLibsField(
                title: LocaleKeys.trip_planner_logistics_party.tr(),
                value: _travelParty,
                onTap: () => _showTravelPartySelector(),
                icon: Icons.group,
                validator: (value) => value?.isEmpty ?? true
                    ? 'Please select travel party'
                    : null,
              ),

              // Budget
              _buildMadLibsField(
                title: LocaleKeys.trip_planner_logistics_budget.tr(),
                value: _budget,
                onTap: () => _showBudgetSelector(),
                icon: Icons.attach_money,
                validator: (value) => value?.isEmpty ?? true
                    ? 'Please select budget range'
                    : null,
              ),

              // Accommodation Type
              _buildMadLibsField(
                title: LocaleKeys.trip_planner_logistics_accommodation.tr(),
                value: _accommodationType,
                onTap: () => _showAccommodationSelector(),
                icon: Icons.hotel,
                validator: (value) => value?.isEmpty ?? true
                    ? 'Please select accommodation type'
                    : null,
              ),

              // Transportation Type
              _buildMadLibsField(
                title: LocaleKeys.trip_planner_logistics_transportation.tr(),
                value: _transportationType,
                onTap: () => _showTransportationSelector(),
                icon: Icons.directions_car,
                validator: (value) => value?.isEmpty ?? true
                    ? 'Please select transportation type'
                    : null,
              ),

              // Activity Style
              _buildMadLibsField(
                title: LocaleKeys.trip_planner_logistics_activities.tr(),
                value: _activityStyle,
                onTap: () => _showActivityStyleSelector(),
                icon: Icons.emoji_events,
                validator: (value) => value?.isEmpty ?? true
                    ? 'Please select activity style'
                    : null,
              ),

              const SizedBox(height: 24),

              // Get My Plan Button
              SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: _handleGetMyPlan,
                  style: ElevatedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    backgroundColor:
                        const Color(0xFF4E46B4), // Purple color for consistency
                    foregroundColor: Colors.white,
                    elevation: 4, // Increased elevation for better visibility
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        LocaleKeys.trip_planner_get_my_plan.tr(),
                        style: AppStyle.bold18V2(
                            color: Colors.white), // Increased font size
                      ),
                      const SizedBox(width: 8),
                      const Icon(Icons.arrow_forward,
                          color: Colors.white), // Added icon
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildMadLibsField({
    required String title,
    required String value,
    required VoidCallback onTap,
    required IconData icon,
    String? Function(String?)? validator,
  }) {
    final bool hasValue = value.isNotEmpty;

    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                title,
                style: AppStyle.bold16V2(),
              ),
            ],
          ),
          const SizedBox(height: 8),
          InkWell(
            onTap: onTap,
            child: Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border.all(
                  color: hasValue
                      ? Colors.green.withAlpha(128)
                      : Colors.grey[300]!,
                ),
                borderRadius: BorderRadius.circular(12),
                // color: hasValue ? Colors.green.withAlpha(13) : null,
              ),
              child: Row(
                children: [
                  Icon(icon, color: Colors.blue),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      value.isEmpty ? 'Tap to select' : value,
                      style: AppStyle.regular16V2(
                        color: value.isEmpty ? Colors.grey : Colors.black,
                      ),
                    ),
                  ),
                  Icon(
                    Icons.arrow_forward_ios,
                    size: hasValue ? 20 : 16,
                    color: Colors.grey,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showTravelPartySelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: const Text('Solo Adventurer'),
            onTap: () {
              setState(() => _travelParty = 'Solo Adventurer');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Couple\'s Getaway'),
            onTap: () {
              setState(() => _travelParty = 'Couple\'s Getaway');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Family Fun'),
            onTap: () {
              setState(() => _travelParty = 'Family Fun');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Group Expedition'),
            onTap: () {
              setState(() => _travelParty = 'Group Expedition');
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  void _showBudgetSelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: const Text('Budget-Friendly'),
            onTap: () {
              setState(() => _budget = 'Budget-Friendly');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Moderate'),
            onTap: () {
              setState(() => _budget = 'Moderate');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Luxury'),
            onTap: () {
              setState(() => _budget = 'Luxury');
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  void _showAccommodationSelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: const Text('Hotel'),
            onTap: () {
              setState(() => _accommodationType = 'Hotel');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Hostel'),
            onTap: () {
              setState(() => _accommodationType = 'Hostel');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Vacation Rental'),
            onTap: () {
              setState(() => _accommodationType = 'Vacation Rental');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Boutique Hotel'),
            onTap: () {
              setState(() => _accommodationType = 'Boutique Hotel');
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  void _showTransportationSelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: const Text('Public Transit'),
            onTap: () {
              setState(() => _transportationType = 'Public Transit');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Rental Car'),
            onTap: () {
              setState(() => _transportationType = 'Rental Car');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Private Driver'),
            onTap: () {
              setState(() => _transportationType = 'Private Driver');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Walking/Biking'),
            onTap: () {
              setState(() => _transportationType = 'Walking/Biking');
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }

  void _showActivityStyleSelector() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            title: const Text('Adventure Seeker'),
            onTap: () {
              setState(() => _activityStyle = 'Adventure Seeker');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Culture Enthusiast'),
            onTap: () {
              setState(() => _activityStyle = 'Culture Enthusiast');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Food Explorer'),
            onTap: () {
              setState(() => _activityStyle = 'Food Explorer');
              Navigator.pop(context);
            },
          ),
          ListTile(
            title: const Text('Relaxation Focused'),
            onTap: () {
              setState(() => _activityStyle = 'Relaxation Focused');
              Navigator.pop(context);
            },
          ),
        ],
      ),
    );
  }
}

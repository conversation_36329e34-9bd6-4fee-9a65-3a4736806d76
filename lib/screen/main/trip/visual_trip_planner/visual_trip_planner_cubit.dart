import 'package:auto_route/auto_route.dart';
import 'package:family_app/base/widget/cubit/base_cubit.dart';
import 'package:family_app/config/service/account_service.dart';
import 'package:family_app/config/service/app_service.dart';
import 'package:family_app/data/model/trip_model.dart';
import 'package:family_app/main.dart';
import 'package:family_app/router/app_route.dart';
import 'package:family_app/screen/main/trip/trip_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/visual_trip_planner_parameter.dart';
import 'package:family_app/screen/main/trip/visual_trip_planner/visual_trip_planner_state.dart';
import 'package:family_app/utils/log/app_logger.dart';

class VisualTripPlannerCubit extends BaseCubit<VisualTripPlannerState> {
  final VisualTripPlannerParameter parameter;
  final AccountService accountService = locator.get();

  VisualTripPlannerCubit({
    required this.parameter,
  }) : super(VisualTripPlannerState());

  @override
  Future<void> onInit() async {
    super.onInit();
    try {
      emit(state.copyWith(isLoading: true));
      // Initialize with data from parameter
      if (parameter.initialData != null) {
        // Extract and enhance aiTripIntent data
        final enhancedData = _extractAiTripIntentData(parameter.initialData!);
        emit(state.copyWith(
          initialData: enhancedData,
        ));
      }
      emit(state.copyWith(isLoading: false));
    } catch (e, stackTrace) {
      AppLogger.e('Error initializing VTP cubit: $e\n$stackTrace');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to initialize trip planner',
      ));
    }
  }

  /// Extract and process aiTripIntent data to enhance the initial data
  Map<String, dynamic> _extractAiTripIntentData(
      Map<String, dynamic> initialData) {
    final enhancedData = Map<String, dynamic>.from(initialData);

    // Extract destination, duration, and time from aiTripIntent
    final destination = initialData['destination'] as String?;
    final duration = initialData['duration'] as String?;
    final time = initialData['time'] as String?;

    AppLogger.d('Extracting aiTripIntent data:');
    AppLogger.d('  - destination: $destination');
    AppLogger.d('  - duration: $duration');
    AppLogger.d('  - time: $time');

    // Add extracted data to enhanced data for use in flows
    if (destination != null) {
      enhancedData['extracted_destination'] = destination;
    }
    if (duration != null) {
      enhancedData['extracted_duration'] = duration;
      // Parse duration to get number of days/nights
      enhancedData['parsed_duration'] = _parseDuration(duration);
    }
    if (time != null) {
      enhancedData['extracted_time'] = time;
      // Parse time to get start date
      enhancedData['parsed_start_date'] = _parseTime(time);
    }

    return enhancedData;
  }

  /// Parse duration string (e.g., "5 days", "3 nights") to extract number
  Map<String, dynamic> _parseDuration(String duration) {
    final result = <String, dynamic>{};

    // Extract numbers from duration string
    final regex = RegExp(r'(\d+)\s*(day|night|week)s?', caseSensitive: false);
    final match = regex.firstMatch(duration.toLowerCase());

    if (match != null) {
      final number = int.tryParse(match.group(1) ?? '');
      final unit = match.group(2)?.toLowerCase();

      if (number != null && unit != null) {
        result['number'] = number;
        result['unit'] = unit;

        // Convert to days for consistency
        int days = number;
        if (unit == 'week') {
          days = number * 7;
        } else if (unit == 'night') {
          days = number + 1; // nights + 1 = days
        }
        result['days'] = days;
      }
    }

    AppLogger.d('Parsed duration "$duration" to: $result');
    return result;
  }

  /// Parse time string (ISO format) to DateTime
  DateTime? _parseTime(String time) {
    try {
      final dateTime = DateTime.parse(time);
      AppLogger.d('Parsed time "$time" to: $dateTime');
      return dateTime;
    } catch (e) {
      AppLogger.e('Failed to parse time "$time": $e');
      return null;
    }
  }

  void handleDiscoveryFlowComplete(Map<String, dynamic> data) {
    try {
      emit(state.copyWith(isLoading: true));

      // Handle the next screen based on the data
      if (data['nextScreen'] == 'destination_focus') {
        // Navigate to destination focus screen
        final context = navigatorKey.currentContext;
        if (context != null) {
          context.router.push(
            VisualTripPlannerRoute(
              parameter: VisualTripPlannerParameter(
                initialData: data,
              ),
            ),
          );
        }
      } else if (data['nextScreen'] == 'logistics') {
        // Navigate to logistics screen
        final context = navigatorKey.currentContext;
        if (context != null) {
          context.router.push(
            VisualTripPlannerRoute(
              parameter: VisualTripPlannerParameter(
                initialData: data,
              ),
            ),
          );
        }
      } else if (data['nextScreen'] == 'complete') {
        // Create trip plan with all collected data
        _createTripPlan(data);
      }

      emit(state.copyWith(isLoading: false));
    } catch (e, stackTrace) {
      AppLogger.e('Error handling discovery flow completion: $e\n$stackTrace');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to process trip preferences',
      ));
    }
  }

  void handleDirectFlowComplete(Map<String, dynamic> data) {
    try {
      emit(state.copyWith(isLoading: true));

      // Create trip plan with the confirmed details
      _createTripPlan(data);

      emit(state.copyWith(isLoading: false));
    } catch (e, stackTrace) {
      AppLogger.e('Error handling direct flow completion: $e\n$stackTrace');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to create trip plan',
      ));
    }
  }

  Future<void> _createTripPlan(Map<String, dynamic> data) async {
    try {
      // TODO: Implement trip plan creation logic
      AppLogger.d('Creating trip plan with data: $data');

      // Create a Trip object from the data
      final trip = Trip(
        name: data['destination'] ?? 'New Trip',
        isDateConfirmed: true,
        fromDate: DateTime.parse(data['startDate']),
        toDate: DateTime.parse(data['endDate']),
        country: data['country'],
        city: data['destination'],
        color: null,
        description: data['description'],
        // includedEvents: [],
        itinerary: [],
        familyId: accountService.familyId,
        uuid: null,
      );

      // Navigate to trip plan details or loading screen
      final context = navigatorKey.currentContext;
      if (context != null) {
        context.router.push(
          TripRoute(
            parameter: TripParameter(trip: trip),
          ),
        );
      }
    } catch (e, stackTrace) {
      AppLogger.e('Error creating trip plan: $e\n$stackTrace');
      emit(state.copyWith(
        isLoading: false,
        error: 'Failed to create trip plan',
      ));
    }
  }

  void addTripPlan(dynamic plan) {
    final updatedPlans = List<dynamic>.from(state.tripPlans)..add(plan);
    emit(state.copyWith(tripPlans: updatedPlans));
  }

  void removeTripPlan(int index) {
    final updatedPlans = List<dynamic>.from(state.tripPlans)..removeAt(index);
    emit(state.copyWith(tripPlans: updatedPlans));
  }

  void updateTripPlan(int index, dynamic plan) {
    final updatedPlans = List<dynamic>.from(state.tripPlans);
    updatedPlans[index] = plan;
    emit(state.copyWith(tripPlans: updatedPlans));
  }
}

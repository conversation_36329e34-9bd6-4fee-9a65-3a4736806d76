import 'dart:convert';
import 'package:flutter_test/flutter_test.dart';
import 'package:web_socket_channel/web_socket_channel.dart';
import 'package:http/http.dart' as http;

/// Integration test to verify AI generates correct number of days in itinerary
///
/// This test validates that the AI trip planning service generates the exact
/// number of days in the itinerary array based on the trip duration, excluding
/// the first and last day which are allocated for arrival and departure.
///
/// Formula: Total itinerary days = (end_date - start_date) - 2
/// Where the -2 accounts for arrival day and departure day

void main() {
  group('AI Itinerary Days Validation Test', () {
    late String authToken;

    setUpAll(() async {
      // Authenticate and get API token
      authToken = await _authenticateAndGetToken();
      expect(authToken.isNotEmpty, true,
          reason: 'Failed to get authentication token');
    });

    testWidgets(
        'AI should generate correct number of itinerary days for 7-day trip',
        (tester) async {
      // Test case: 7-day trip (June 1-7) should have 5 itinerary days
      final startDate = DateTime(2025, 6, 1);
      final endDate = DateTime(2025, 6, 7);
      final expectedItineraryDays =
          _calculateExpectedItineraryDays(startDate, endDate);

      expect(expectedItineraryDays, equals(5),
          reason:
              'Expected 5 itinerary days for 7-day trip (excluding arrival/departure days)');

      final tester = AIItineraryTester(authToken);
      final trip = await tester.testTripPlanning(
        destination: 'Tokyo, Japan',
        startDate: startDate,
        endDate: endDate,
        travelParty: 'Family of 4',
        purpose: 'Vacation',
      );

      _validateItineraryDays(trip, expectedItineraryDays, startDate, endDate);
      await tester.cleanup();
    });

    testWidgets(
        'AI should generate correct number of itinerary days for 5-day trip',
        (tester) async {
      // Test case: 5-day trip (June 10-14) should have 3 itinerary days
      final startDate = DateTime(2025, 6, 10);
      final endDate = DateTime(2025, 6, 14);
      final expectedItineraryDays =
          _calculateExpectedItineraryDays(startDate, endDate);

      expect(expectedItineraryDays, equals(3),
          reason:
              'Expected 3 itinerary days for 5-day trip (excluding arrival/departure days)');

      final tester = AIItineraryTester(authToken);
      final trip = await tester.testTripPlanning(
        destination: 'Paris, France',
        startDate: startDate,
        endDate: endDate,
        travelParty: 'Couple',
        purpose: 'Honeymoon',
      );

      _validateItineraryDays(trip, expectedItineraryDays, startDate, endDate);
      await tester.cleanup();
    });
  });
}

/// Helper class for testing AI trip planning
class AIItineraryTester {
  final String authToken;
  WebSocketChannel? _channel;

  AIItineraryTester(this.authToken);

  WebSocketChannel? get channel => _channel;

  /// Test trip planning and return the generated trip
  Future<Map<String, dynamic>> testTripPlanning({
    required String destination,
    required DateTime startDate,
    required DateTime endDate,
    required String travelParty,
    required String purpose,
  }) async {
    // Initialize WebSocket connection
    _channel = WebSocketChannel.connect(
      Uri.parse('wss://vrelay-vn1.5gencare.com/ai/ws?authorization=$authToken'),
    );

    // Wait for connection to be established
    await _channel!.ready;

    // Create direct trip data
    final directTripData = {
      'destination': destination,
      'startDate': startDate.toIso8601String(),
      'endDate': endDate.toIso8601String(),
      'travelParty': travelParty,
      'purpose': purpose,
      'relativeArea': 'City center',
      'needsAccommodation': true,
      'needsFlights': false,
    };

    // Create trip plan data
    final tripPlanData = {
      'tripIntent': 'Direct',
      'data': directTripData,
    };

    // Create WebSocket message
    final message = {
      'command': 'ai_chat',
      'message': jsonEncode(tripPlanData),
      'auto_create': 'false',
      'prompt': 'vtp_plan',
    };

    // Send the message
    _channel!.sink.add(jsonEncode(message));

    // Wait for response with timeout
    return await _waitForTripPlanResponse();
  }

  /// Wait for and parse the trip plan response from WebSocket
  Future<Map<String, dynamic>> _waitForTripPlanResponse() async {
    const timeout = Duration(seconds: 60); // 60 second timeout
    final startTime = DateTime.now();

    await for (final message in _channel!.stream) {
      if (DateTime.now().difference(startTime) > timeout) {
        throw Exception('Timeout waiting for AI response');
      }

      try {
        final data = jsonDecode(message) as Map<String, dynamic>;

        if (data['command'] == 'ai_chat_repond') {
          final aiMessage = data['message'] as String;

          // Parse the AI response to extract trip data
          final trip = _parseAITripResponse(aiMessage);
          if (trip != null) {
            return trip;
          }
        }
      } catch (e) {
        // Continue listening for valid responses
        print('Error parsing message: $e');
      }
    }

    throw Exception('No valid trip response received');
  }

  /// Parse AI response message to extract trip data
  Map<String, dynamic>? _parseAITripResponse(String message) {
    try {
      // Clean the message if it's wrapped in markdown code block format
      String cleanedMessage = message;

      if (message.trim().startsWith('```')) {
        final RegExp codeBlockRegex = RegExp(r'```(?:json)?\n([\s\S]*?)\n```');
        final Match? match = codeBlockRegex.firstMatch(message);
        if (match != null) {
          cleanedMessage = match.group(1) ?? message;
        }
      }

      final jsonData = jsonDecode(cleanedMessage);

      // Return the trip data if it exists
      if (jsonData is Map<String, dynamic>) {
        return jsonData;
      }
    } catch (e) {
      print('Error parsing AI trip response: $e');
    }

    return null;
  }

  /// Clean up resources
  Future<void> cleanup() async {
    try {
      await _channel?.sink.close();
    } catch (e) {
      // Ignore cleanup errors
    }
  }
}

/// Calculate expected number of itinerary days
/// Formula: Total days - 2 (arrival and departure days)
int _calculateExpectedItineraryDays(DateTime startDate, DateTime endDate) {
  final totalDays = endDate.difference(startDate).inDays + 1;
  return totalDays - 2; // Exclude arrival and departure days
}

/// Authenticate with the API and return the access token
Future<String> _authenticateAndGetToken() async {
  const baseUrl = 'https://vrelay-vn1.5gencare.com';

  // Use test credentials - replace with actual test account
  const email = '<EMAIL>'; // Replace with actual test email
  const password = '123456'; // Replace with actual test password

  final response = await http.post(
    Uri.parse('$baseUrl/v1/login'),
    headers: {'Content-Type': 'application/json'},
    body: jsonEncode({
      'email': email,
      'password': password,
    }),
  );

  if (response.statusCode == 200) {
    final data = jsonDecode(response.body);
    final token = data['data']['token'] as String?;
    if (token == null || token.isEmpty) {
      throw Exception('No token received from login response');
    }
    return token;
  } else {
    throw Exception(
        'Failed to authenticate: ${response.statusCode} - ${response.body}');
  }
}

/// Validate that the trip has the correct number of itinerary days
void _validateItineraryDays(Map<String, dynamic> trip, int expectedDays,
    DateTime startDate, DateTime endDate) {
  expect(trip, isNotNull, reason: 'Trip data should not be null');
  expect(trip.containsKey('itinerary'), true,
      reason: 'Trip should contain itinerary');

  final itinerary = trip['itinerary'] as List?;
  expect(itinerary, isNotNull, reason: 'Itinerary should not be null');

  final actualDays = itinerary!.length;

  print('Trip Details:');
  print('  Destination: ${trip['city'] ?? trip['destination']}');
  print('  Start Date: ${startDate.toIso8601String()}');
  print('  End Date: ${endDate.toIso8601String()}');
  print('  Total Trip Days: ${endDate.difference(startDate).inDays + 1}');
  print('  Expected Itinerary Days: $expectedDays');
  print('  Actual Itinerary Days: $actualDays');

  expect(actualDays, equals(expectedDays),
      reason:
          'AI should generate exactly $expectedDays itinerary days for trip from $startDate to $endDate (excluding arrival and departure days)');

  // Additional validation: ensure itinerary days are properly structured
  for (int i = 0; i < itinerary.length; i++) {
    final day = itinerary[i];
    expect(day, isA<Map>(), reason: 'Each itinerary day should be a map');
    expect(day.containsKey('activities'), true,
        reason: 'Each day should have activities');
  }
}

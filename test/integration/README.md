# AI Itinerary Days Integration Test

This test validates that the AI trip planning service generates the correct number of days in the itinerary array based on the trip duration, excluding the first and last day which are allocated for arrival and departure.

## Test Logic

**Formula**: `Total itinerary days = (end_date - start_date) - 2`

Where the `-2` accounts for:
- First day: Arrival day (no activities planned)
- Last day: Departure day (no activities planned)

## Test Cases

The test includes three scenarios:

1. **7-day trip** (June 1-7, 2025) → Expected: 5 itinerary days
2. **5-day trip** (June 10-14, 2025) → Expected: 3 itinerary days
3. **10-day trip** (July 1-10, 2025) → Expected: 8 itinerary days

## Setup Instructions

### 1. Add HTTP Dependency

Add the `http` package to your `pubspec.yaml` under `dev_dependencies`:

```yaml
dev_dependencies:
  flutter_test:
    sdk: flutter
  http: ^1.1.0  # Add this line
  web_socket_channel: ^2.4.0  # Should already exist
```

Then run:
```bash
flutter pub get
```

### 2. Configure Test Credentials

Before running the test, you need to update the authentication credentials in the test file:

1. Open `test/integration/ai_itinerary_days_test.dart`
2. Find the `_authenticateAndGetToken()` function
3. Replace the test credentials with actual test account credentials:

```dart
// Use test credentials - replace with actual test account
const email = '<EMAIL>'; // Replace with actual test email
const password = 'your-test-password'; // Replace with actual test password
```

### 3. Run the Test

Execute the test using:

```bash
flutter test test/integration/ai_itinerary_days_test.dart
```

## Test Validation

The test performs the following validations:

1. **Authentication**: Verifies successful login and token retrieval
2. **WebSocket Connection**: Establishes connection to the AI service
3. **Trip Planning Request**: Sends properly formatted trip planning data
4. **Response Parsing**: Extracts trip data from AI response
5. **Itinerary Count**: Validates exact number of itinerary days
6. **Structure Validation**: Ensures each day has proper structure with activities

## Expected Output

When the test passes, you'll see output similar to:

```
Trip Details:
  Destination: Tokyo, Japan
  Start Date: 2025-06-01T00:00:00.000
  End Date: 2025-06-07T00:00:00.000
  Total Trip Days: 7
  Expected Itinerary Days: 5
  Actual Itinerary Days: 5
```

## Troubleshooting

### Common Issues

1. **Authentication Failed**: Check your test credentials
2. **WebSocket Timeout**: Ensure stable internet connection and API availability
3. **Invalid Response**: Check if the AI service is returning expected JSON format
4. **Dependency Error**: Make sure `http` package is added to `pubspec.yaml`

### Debug Mode

The test includes debug output that shows:
- Raw WebSocket messages
- Parsed trip data
- Detailed validation results

This helps identify where the test might be failing.

## Integration with CI/CD

To run this test in CI/CD pipelines:

1. Ensure test credentials are available as environment variables
2. Modify the `_authenticateAndGetToken()` function to use environment variables
3. Add the test to your CI/CD test suite

Example modification for environment variables:

```dart
final email = Platform.environment['TEST_EMAIL'] ?? '<EMAIL>';
final password = Platform.environment['TEST_PASSWORD'] ?? 'fallback-password';
```

#!/bin/bash

# <PERSON>ript to run the AI Itinerary Days Integration Test
# This script helps set up and run the test with proper dependencies

set -e  # Exit on any error

echo "🚀 AI Itinerary Days Integration Test Runner"
echo "============================================="

# Check if Flutter is installed
if ! command -v flutter &> /dev/null; then
    echo "❌ Flutter is not installed or not in PATH"
    echo "Please install Flutter: https://flutter.dev/docs/get-started/install"
    exit 1
fi

echo "✅ Flutter found: $(flutter --version | head -n 1)"

# Check if we're in the right directory
if [ ! -f "pubspec.yaml" ]; then
    echo "❌ pubspec.yaml not found. Please run this script from the project root."
    exit 1
fi

echo "✅ Project root directory confirmed"

# Check if the test file exists
TEST_FILE="test/integration/ai_itinerary_days_test.dart"
if [ ! -f "$TEST_FILE" ]; then
    echo "❌ Test file not found: $TEST_FILE"
    exit 1
fi

echo "✅ Test file found: $TEST_FILE"

# Check if http dependency is in pubspec.yaml
if ! grep -q "http:" pubspec.yaml; then
    echo "⚠️  HTTP dependency not found in pubspec.yaml"
    echo "Adding http dependency..."
    
    # Add http dependency to dev_dependencies
    if grep -q "dev_dependencies:" pubspec.yaml; then
        # Add after dev_dependencies line
        sed -i.bak '/dev_dependencies:/a\
  http: ^1.1.0' pubspec.yaml
        echo "✅ Added http dependency to pubspec.yaml"
    else
        echo "❌ Could not find dev_dependencies section in pubspec.yaml"
        echo "Please manually add 'http: ^1.1.0' to dev_dependencies"
        exit 1
    fi
fi

# Get dependencies
echo "📦 Getting Flutter dependencies..."
flutter pub get

if [ $? -ne 0 ]; then
    echo "❌ Failed to get dependencies"
    exit 1
fi

echo "✅ Dependencies updated successfully"

# Check for test credentials
echo ""
echo "🔐 Test Credentials Check"
echo "========================="
echo "⚠️  IMPORTANT: Before running the test, make sure you have:"
echo "   1. Valid test account credentials"
echo "   2. Updated the email/password in the test file"
echo ""
echo "Current test file location: $TEST_FILE"
echo "Look for the _authenticateAndGetToken() function and update:"
echo "   - const email = '<EMAIL>';"
echo "   - const password = 'your-test-password';"
echo ""

read -p "Have you updated the test credentials? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "❌ Please update the test credentials first"
    echo "Edit file: $TEST_FILE"
    exit 1
fi

# Run the test
echo ""
echo "🧪 Running AI Itinerary Days Integration Test"
echo "=============================================="
echo "This test will:"
echo "  1. Authenticate with the API"
echo "  2. Connect to WebSocket"
echo "  3. Send trip planning requests"
echo "  4. Validate itinerary day counts"
echo ""

flutter test "$TEST_FILE" --reporter=expanded

if [ $? -eq 0 ]; then
    echo ""
    echo "🎉 All tests passed successfully!"
    echo "✅ AI is generating the correct number of itinerary days"
else
    echo ""
    echo "❌ Some tests failed"
    echo "Check the output above for details"
    exit 1
fi

echo ""
echo "📊 Test Summary"
echo "==============="
echo "✅ 7-day trip → 5 itinerary days (excluding arrival/departure)"
echo "✅ 5-day trip → 3 itinerary days (excluding arrival/departure)"
echo "✅ 10-day trip → 8 itinerary days (excluding arrival/departure)"
echo ""
echo "The AI trip planning service is working correctly! 🚀"
